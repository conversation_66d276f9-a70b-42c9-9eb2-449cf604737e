import { <PERSON>ada<PERSON> } from "next"
import Link from "next/link"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"

export const metadata: Metadata = {
  title: "Blog - Wellness Marketing Maestros",
  description: "Expert insights and strategies for wellness clinic marketing, patient acquisition, and practice growth.",
}

const blogPosts = [
  {
    slug: "boosting-revenue-overnight-with-text-message-reminders-for-medical-appointments",
    title: "Boosting Revenue Overnight with Text Message Reminders for Medical Appointments",
    excerpt: "Boost your revenue with text message reminders! Discover how this simple strategy can significantly increase your medical appointment bookings and keep your schedule full.",
    category: "Patient Engagement",
    readTime: "8 min read",
    publishDate: "2025-01-15",
  },
  {
    slug: "navigating-through-value-propositions-deciphering-the-offer-in-holistic-healthcare-marketing",
    title: "Navigating Through Value Propositions: Deciphering The Offer in Holistic Healthcare Marketing",
    excerpt: "Explore the importance of your Offer Value Proposition (OVP) in holistic healthcare marketing. Learn how to craft compelling OVPs to turn prospects into devoted patients.",
    category: "Marketing Strategy",
    readTime: "6 min read",
    publishDate: "2025-01-10",
  },
  {
    slug: "power-of-unique-value-proposition-and-offer-value-proposition-in-healthcare-marketing",
    title: "The Power Intersection of Unique Value Proposition and Offer Value Proposition in Healthcare Marketing",
    excerpt: "Explore the dynamic interplay between Unique Value Proposition (UVP) and Offer Value Proposition (OVP) in healthcare marketing. This in-depth analysis provides insights into effective differentiation and connection strategies.",
    category: "Marketing Strategy",
    readTime: "7 min read",
    publishDate: "2025-01-05",
  },
  {
    slug: "clear-communication-in-marketing-alternative-healthcare-services",
    title: "Understanding the Importance of Clear Communication in Marketing Alternative Healthcare Services",
    excerpt: "The power of clear, tailored communication in the holistic healthcare industry. Learn how understanding your audience and redefining competition can help foster growth, expand your influence, and bridge the gap between conventional and alternative medicine.",
    category: "Communication",
    readTime: "5 min read",
    publishDate: "2025-01-01",
  },
]

export default function BlogPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-slate-900 mb-6">
            Wellness Marketing Insights
          </h1>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
            Expert strategies, proven tactics, and actionable insights to grow your wellness practice, 
            attract more patients, and build a thriving healthcare business.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {blogPosts.map((post) => (
            <Card key={post.slug} className="group hover:shadow-xl transition-all duration-300 border-0 bg-white">
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between mb-3">
                  <Badge variant="secondary" className="bg-healing-green/10 text-healing-green hover:bg-healing-green/20">
                    {post.category}
                  </Badge>
                  <span className="text-sm text-slate-500">{post.readTime}</span>
                </div>
                <CardTitle className="text-xl font-bold text-slate-900 group-hover:text-strategy-blue transition-colors duration-200 line-clamp-2">
                  <Link href={`/blog/${post.slug}`} className="hover:underline">
                    {post.title}
                  </Link>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-slate-600 text-base leading-relaxed mb-4 line-clamp-3">
                  {post.excerpt}
                </CardDescription>
                <div className="flex items-center justify-end">
                  <Link 
                    href={`/blog/${post.slug}`}
                    className="inline-flex items-center text-strategy-blue hover:text-strategy-blue/80 font-medium text-sm transition-colors duration-200"
                  >
                    Read Article
                    <svg className="ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

      </div>
    </div>
  )
}